/*********************************************************************
 * * Copyright (C), 2022, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2022/3/7
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.base.view

interface IRecyclerAdapterData {
    fun getItemCount(): Int

    fun getRealItemCount(): Int = getItemCount()

    fun getRealPosInViewType(position: Int): Int {
        return position
    }
}