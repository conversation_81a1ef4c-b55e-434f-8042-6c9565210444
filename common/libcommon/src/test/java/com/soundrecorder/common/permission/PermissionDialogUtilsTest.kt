package com.soundrecorder.common.permission

import android.Manifest.permission.POST_NOTIFICATIONS
import android.Manifest.permission.READ_EXTERNAL_STORAGE
import android.Manifest.permission.READ_MEDIA_AUDIO
import android.Manifest.permission.READ_MEDIA_IMAGES
import android.Manifest.permission.RECORD_AUDIO
import android.Manifest.permission.WRITE_EXTERNAL_STORAGE
import android.content.DialogInterface.BUTTON_NEGATIVE
import android.content.DialogInterface.BUTTON_POSITIVE
import android.os.Build
import android.os.Looper
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.soundrecorder.base.utils.BaseUtil
import com.soundrecorder.common.permission.PermissionDialogUtils.TYPE_DIALOG_TINY
import com.soundrecorder.common.permission.PermissionDialogUtils.showPermissionAllFileAccessDialog
import com.soundrecorder.common.shadows.ShadowAppFeatureUtil
import com.soundrecorder.common.shadows.ShadowCOUIVersionUtil
import com.soundrecorder.common.shadows.ShadowFeatureOption
import com.soundrecorder.common.shadows.ShadowOS12FeatureUtil
import org.junit.After
import org.junit.Assert
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.MockedStatic
import org.robolectric.Robolectric
import org.robolectric.Shadows.shadowOf
import org.robolectric.android.controller.ActivityController
import org.robolectric.annotation.Config

@RunWith(AndroidJUnit4::class)
@Config(
    sdk = [Build.VERSION_CODES.S],
    shadows = [ShadowOS12FeatureUtil::class, ShadowFeatureOption::class, ShadowCOUIVersionUtil::class, ShadowAppFeatureUtil::class]
)
class PermissionDialogUtilsTest {
    private var controller: ActivityController<PermissionProxyActivity>? = null
    private var mockedStatic: MockedStatic<BaseUtil>? = null

    @Before
    fun init() {
        controller = Robolectric.buildActivity(PermissionProxyActivity::class.java)
    }

    @After
    fun clear() {
        controller = null
        mockedStatic?.close()
        mockedStatic = null
    }

    @Test
    fun check_dialog() {
        val act = controller?.create()?.get() ?: return
        shadowOf(Looper.getMainLooper()).idle()
        val listener = object : PermissionDialogUtils.PermissionDialogListener {
            override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
                println("alertType = $alertType , isOk = $isOk , permissions = $permissions")
            }

            override fun onBackPress(alertType: Int) {
                println("alertType = $alertType")
            }
        }
        Assert.assertNull(PermissionDialogUtils.showPermissionsDialog(act, listener, arrayOf()))
        arrayOf(
            arrayOf(READ_MEDIA_AUDIO),
            arrayOf(READ_MEDIA_IMAGES),
            arrayOf(RECORD_AUDIO),
            arrayOf(READ_EXTERNAL_STORAGE),
            arrayOf(POST_NOTIFICATIONS),
            arrayOf(""),
            arrayOf(RECORD_AUDIO, READ_MEDIA_AUDIO),
            arrayOf(RECORD_AUDIO, READ_EXTERNAL_STORAGE),
            arrayOf(READ_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE),
            arrayOf(READ_MEDIA_AUDIO, POST_NOTIFICATIONS),
        ).forEach {
            mockedStatic?.`when`<Boolean> { BaseUtil.isAndroidTOrLater }?.thenReturn(true)
            Assert.assertNotNull(PermissionDialogUtils.showPermissionsDialog(act, listener, it))
            mockedStatic?.`when`<Boolean> { BaseUtil.isAndroidTOrLater }?.thenReturn(false)
            Assert.assertNotNull(PermissionDialogUtils.showPermissionsDialog(act, listener, it))
            Assert.assertNotNull(showPermissionAllFileAccessDialog(act, listener))
        }
        arrayOf(
            arrayOf(READ_MEDIA_AUDIO),
            arrayOf(READ_MEDIA_IMAGES),
            arrayOf(RECORD_AUDIO),
            arrayOf(READ_EXTERNAL_STORAGE),
            arrayOf("")
        ).forEach {
            val dialog = PermissionDialogUtils.showPermissionsDialog(act, listener, it)
            dialog?.getButton(BUTTON_NEGATIVE)?.callOnClick()
            val allFileDialog = showPermissionAllFileAccessDialog(act, listener)
            allFileDialog.getButton(BUTTON_NEGATIVE)?.callOnClick()
        }
        arrayOf(
            arrayOf(READ_MEDIA_AUDIO),
            arrayOf(READ_MEDIA_IMAGES),
            arrayOf(RECORD_AUDIO),
            arrayOf(READ_EXTERNAL_STORAGE),
            arrayOf("")
        ).forEach {
            val dialog = PermissionDialogUtils.showPermissionsDialog(act, listener, it)
            val button = dialog?.getButton(BUTTON_POSITIVE)
            Assert.assertNotNull(button)
            button?.callOnClick()
            val allFileDialog = showPermissionAllFileAccessDialog(act, listener)
            allFileDialog.getButton(BUTTON_POSITIVE)?.callOnClick()
        }

        arrayOf(
            arrayOf(READ_MEDIA_AUDIO),
            arrayOf(READ_MEDIA_IMAGES),
            arrayOf(RECORD_AUDIO),
            arrayOf(READ_EXTERNAL_STORAGE),
            arrayOf("")
        ).forEach {
            val dialog = PermissionDialogUtils.showPermissionsDialog(act, listener, it)
            dialog?.setCancelable(true)
            dialog?.cancel()
            val allFileDialog = showPermissionAllFileAccessDialog(act, listener)
            allFileDialog.setCancelable(true)
            allFileDialog.cancel()
        }
    }

    @Test
    fun should_showNotificationDialog_when_showPermissionsDialog() {
        val act = controller?.create()?.get() ?: return
        shadowOf(Looper.getMainLooper()).idle()
        val listener = object : PermissionDialogUtils.PermissionDialogListener {
            override fun onClick(alertType: Int, isOk: Boolean, permissions: ArrayList<String>?) {
            }

            override fun onBackPress(alertType: Int) {
            }

            override fun dialogPermissionType(dialogPermissionType: Int) {
                Assert.assertEquals(PermissionDialogUtils.TYPE_PERMISSION_POST_NOTIFICATION, dialogPermissionType)
            }
        }
        val notificationDialog = PermissionDialogUtils.showPermissionsDialog(act, listener, arrayOf(POST_NOTIFICATIONS), TYPE_DIALOG_TINY)
        Assert.assertNotNull(notificationDialog)
        Assert.assertTrue(notificationDialog?.isShowing == true)
    }
}