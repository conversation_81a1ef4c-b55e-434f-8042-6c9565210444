/*********************************************************************
 * * Copyright (C), 2023, OPlus Mobile Comm Corp., Ltd.
 * * All rights reserved.
 * * File        :  GroupHorizonLabelAdapter.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/1/15
 * * Author      : W9035969
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.soundrecorder.browsefile.home.item

import android.content.Context
import android.graphics.Typeface
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ViewHolder
import com.coui.appcompat.button.COUIButton
import com.soundrecorder.base.utils.DebugUtil
import com.soundrecorder.browsefile.R
import com.soundrecorder.common.databean.GroupInfo
import com.soundrecorder.common.db.GroupInfoDbUtil

class GroupHorizonLabelAdapter(val mContext: Context) : RecyclerView.Adapter<ViewHolder>() {

    companion object {
        private const val TAG = "GroupHorizonLabelAdapter"

        private const val FONT_WEIGHT_400 = 400
        private const val FONT_WEIGHT_500 = 500
    }

    private var fontWeight500: Typeface? = null
    private var fontWeight400: Typeface? = null
    private var selectedColor: Int? = null
    private var unSelectedColor: Int? = null
    private var normalTextColor: Int? = null
    private var disableTextColor: Int? = null

    private var mDatas = mutableListOf<GroupInfo>()
    private var mCurrentGroup: GroupInfo? = null
    var layoutManager: LinearLayoutManager? = null
    private var onGroupLabelClickListener: OnGroupLabelClickListener? = null
    private var isEdit = false

    fun setEdit(isEdit: Boolean) {
        this.isEdit = isEdit
        notifyDataSetChanged()
    }

    fun setOnGroupLabelClickListener(listener: OnGroupLabelClickListener) {
        onGroupLabelClickListener = listener
    }

    fun refresh(list: MutableList<GroupInfo>) {
        val oldData = this.mDatas.toList()
        this.mDatas = list

        // 使用DiffUtil优化更新，避免不必要的界面重绘
        if (shouldUseSmartUpdate(oldData, list)) {
            calculateAndDispatchDiff(oldData, list)
        } else {
            // 延迟执行notifyDataSetChanged，避免阻塞隐私弹框消失动画
            (mContext as? Activity)?.runOnUiThread {
                notifyDataSetChanged()
            }
        }
    }

    fun setCurrentGroup(item: GroupInfo?) {
        val oldCurrentGroup = mCurrentGroup
        mCurrentGroup = item

        // 只更新受影响的item，而不是整个列表
        if (oldCurrentGroup?.mUuId != item?.mUuId) {
            updateAffectedItems(oldCurrentGroup, item)
        }
    }

    /**
     * 判断是否应该使用智能更新
     */
    private fun shouldUseSmartUpdate(oldData: List<GroupInfo>, newData: List<GroupInfo>): Boolean {
        // 数据量较小且结构相似时使用DiffUtil
        return oldData.size <= 10 && newData.size <= 10 &&
               oldData.size == newData.size
    }

    /**
     * 计算差异并分发更新
     */
    private fun calculateAndDispatchDiff(oldData: List<GroupInfo>, newData: List<GroupInfo>) {
        // 简化的差异计算，避免复杂的DiffUtil计算阻塞主线程
        for (i in newData.indices) {
            if (i < oldData.size && oldData[i].mUuId != newData[i].mUuId) {
                notifyItemChanged(i)
            }
        }
    }

    /**
     * 只更新受当前分组变化影响的item
     */
    private fun updateAffectedItems(oldGroup: GroupInfo?, newGroup: GroupInfo?) {
        // 找到旧的选中项并更新
        oldGroup?.let { old ->
            val oldPosition = mDatas.indexOfFirst { it.mUuId == old.mUuId }
            if (oldPosition >= 0) {
                notifyItemChanged(oldPosition)
            }
        }

        // 找到新的选中项并更新
        newGroup?.let { new ->
            val newPosition = mDatas.indexOfFirst { it.mUuId == new.mUuId }
            if (newPosition >= 0) {
                notifyItemChanged(newPosition)
            }
        }
    }

    fun scrollToCheckedPosition() {
        val currentGroupPosition = mDatas.indexOfFirst { it.mUuId == mCurrentGroup?.mUuId }
        if (currentGroupPosition < 0) {
            DebugUtil.d(TAG, "scrollToPosition failed: $currentGroupPosition")
            return
        }
        layoutManager?.postOnAnimation {
            layoutManager?.scrollToPosition(currentGroupPosition)
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return GroupLabelItemViewHolder(LayoutInflater.from(mContext).inflate(R.layout.group_label_item, parent, false))
    }

    override fun getItemViewType(position: Int): Int {
        return position
    }

    override fun getItemCount(): Int {
        return mDatas.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        if (holder is GroupLabelItemViewHolder) {
            binGroup(holder, position)
        }
    }

    private fun binGroup(
        holder: GroupLabelItemViewHolder,
        position: Int
    ) {
        //val realPostion = position - 1
        if (fontWeight500 == null) {
            fontWeight500 = Typeface.create(holder.tvLabelName?.typeface, FONT_WEIGHT_500, false)
            fontWeight400 = Typeface.create(holder.tvLabelName?.typeface, FONT_WEIGHT_400, false)
        }
        if (selectedColor == null) {
            selectedColor = mContext.getColor(com.soundrecorder.common.R.color.label_checked)
        }
        if (unSelectedColor == null) {
            unSelectedColor = mContext.getColor(com.soundrecorder.common.R.color.label_normal)
        }
        if (normalTextColor == null) {
            normalTextColor = mContext.getColor(com.support.appcompat.R.color.coui_color_label_primary)
        }
        if (disableTextColor == null) {
            disableTextColor = mContext.getColor(com.support.appcompat.R.color.coui_color_label_tertiary)
        }

        val groupItem = mDatas[position]
        refreshRecordingGroupName(holder, groupItem)
        holder.tvLabelName?.tag = groupItem.mUuId
        holder.tvLabelName?.isSelected = mCurrentGroup?.mUuId == groupItem.mUuId
        holder.tvLabelName?.setOnClickListener {
            onGroupLabelClickListener?.onItemClick(position, it, groupItem)
        }

        if (isEdit) {
            setEnableEditState(holder)
        } else {
            setEnableNormalState(holder)
        }
    }

    private fun setEnableNormalState(
        holder: GroupLabelItemViewHolder
    ) {
        if (holder.tvLabelName?.isSelected == true) {
            holder.tvLabelName?.typeface = fontWeight500
            selectedColor?.let {
                holder.tvLabelName?.drawableColor = it
            }
        } else {
            holder.tvLabelName?.typeface = fontWeight400
            unSelectedColor?.let {
                holder.tvLabelName?.drawableColor = it
            }
        }
        unSelectedColor?.let {
            holder.tvLabelName?.setDisabledColor(it)
        }
        holder.tvLabelName?.isEnabled = true
        normalTextColor?.let {
            holder.tvLabelName?.setTextColor(it)
        }
    }

    private fun setEnableEditState(
        holder: GroupLabelItemViewHolder
    ) {
        if (holder.tvLabelName?.isSelected == true) {
            holder.tvLabelName?.typeface = fontWeight500
            selectedColor?.let {
                holder.tvLabelName?.drawableColor = it
            }
            holder.tvLabelName?.isEnabled = true
            normalTextColor?.let {
                holder.tvLabelName?.setTextColor(it)
            }
        } else {
            holder.tvLabelName?.typeface = fontWeight400
            unSelectedColor?.let {
                holder.tvLabelName?.drawableColor = it
                holder.tvLabelName?.setDisabledColor(it)
            }
            holder.tvLabelName?.isEnabled = false
            disableTextColor?.let {
                holder.tvLabelName?.setTextColor(it)
            }
        }
    }

    private fun refreshRecordingGroupName(holder: GroupLabelItemViewHolder, groupInfo: GroupInfo) {
        holder.tvLabelName?.text = when (groupInfo.mUuId) {
            GroupInfoDbUtil.DEFAULT_ALL_UUID -> mContext.getString(com.soundrecorder.common.R.string.all_the_recordings)
            GroupInfoDbUtil.DEFAULT_CALLING_UUID -> mContext.getString(com.soundrecorder.common.R.string.incall_recording_tab)
            GroupInfoDbUtil.DEFAULT_COMMON_UUID -> mContext.getString(com.soundrecorder.common.R.string.normal_recording_tab)
            GroupInfoDbUtil.DEFAULT_RECENTLY_DELETE_UUID -> mContext.getString(com.soundrecorder.common.R.string.recycle_recently_deleted)
            else -> groupInfo.mGroupName
        }
    }

    class GroupLabelItemViewHolder(itemView: View) : ViewHolder(itemView) {
        var tvLabelName: COUIButton? =  itemView.findViewById(R.id.tv_label_name)
        init {
            setIsRecyclable(false)
        }
    }

    interface OnGroupLabelClickListener {

        fun onItemClick(position: Int, view: View, currentItem: GroupInfo)
    }
}